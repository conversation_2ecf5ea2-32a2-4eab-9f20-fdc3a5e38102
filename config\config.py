e# src/config.py

import os
from typing import Dict, Any

class Config:
    """
    系统配置类，管理所有可配置的参数
    """

    @classmethod
    def _get_bool_env(cls, key: str, default: str) -> bool:
        """获取布尔类型的环境变量"""
        return os.getenv(key, default).lower() == "true"

    @classmethod
    def _get_int_env(cls, key: str, default: str) -> int:
        """获取整数类型的环境变量"""
        return int(os.getenv(key, default))
    
    # AI服务配置
    AI_SERVICE_API_KEY = os.getenv("AI_SERVICE_API_KEY", "sk-mT8uDsuxCwxMPUlwdodDOcdjzJjOYFBQQWw17LLLDPXyqVH0")
    AI_SERVICE_BASE_URL = os.getenv("AI_SERVICE_BASE_URL", "http://47.102.193.166:8060")
    AI_SERVICE_EMBEDDING_URL = os.getenv("AI_SERVICE_EMBEDDING_URL", "http://10.201.64.106:30000/v1")
    AI_SERVICE_RERANKER_URL = os.getenv("AI_SERVICE_RERANKER_URL", "http://10.201.64.106:30001/v1")
    
    # 数据库配置
    ELASTICSEARCH_URL = os.getenv("ELASTICSEARCH_URL", "http://10.201.25.42:9200")
    ELASTICSEARCH_TIMEOUT = int(os.getenv("ELASTICSEARCH_TIMEOUT", "300"))
    ELASTICSEARCH_MAX_RETRIES = int(os.getenv("ELASTICSEARCH_MAX_RETRIES", "3"))
    
    # 信念模块配置
    BELIEF_EMBEDDING_THRESHOLD = float(os.getenv("BELIEF_EMBEDDING_THRESHOLD", "0.9"))
    BELIEF_RERANK_THRESHOLD = float(os.getenv("BELIEF_RERANK_THRESHOLD", "0.2"))
    BELIEF_CONFLICT_THRESHOLD = float(os.getenv("BELIEF_CONFLICT_THRESHOLD", "0.05"))

    # 模拟运行配置
    SIMULATION_STEPS = 5  # 每轮模拟步数
    SIMULATION_ROUNDS = 40  # 模拟轮数

    # 用户配置
    NUM_USERS = 100  # 创建用户数量
    INTELLECTUAL_RATIO = 0.5  # 知识分子比例
    INTELLECTUAL_BELIEF_MIN = 2  # 知识分子信念数量最小值
    INTELLECTUAL_BELIEF_MAX = 3  # 知识分子信念数量最大值
    REGULAR_BELIEF_MIN = 0  # 普通群众信念数量最小值
    REGULAR_BELIEF_MAX = 3  # 普通群众信念数量最大值

    # 知识分子用户配置
    ADD_INTELLECTUALS = False  # 是否添加知识分子用户
    ADD_ROUND = 61  # 从第几轮开始添加知识分子用户
    NUM_INTELLECTUALS = 60  # 要添加的知识分子用户总数量

    # 群体间关注关系配置 - 引入跨群体关注实验
    # 普通群众关注知识分子的比例范围（群众更多关注权威）
    REGULAR_TO_INTELLECTUAL_FOLLOW_RATIO_MIN = 0.15  # 最小比例（从0增至0.15）
    REGULAR_TO_INTELLECTUAL_FOLLOW_RATIO_MAX = 0.35  # 最大比例（从0增至0.35）

    # 知识分子关注普通群众的比例范围 - 新增跨群体关注（知识分子较少关注群众）
    INTELLECTUAL_TO_REGULAR_FOLLOW_RATIO_MIN = 0.03  # 最小比例
    INTELLECTUAL_TO_REGULAR_FOLLOW_RATIO_MAX = 0.08  # 最大比例

    # 知识分子之间相互关注的比例范围 - 降低连接密度实验
    INTELLECTUAL_TO_INTELLECTUAL_FOLLOW_RATIO_MIN = 0.2  # 最小比例（从0.5降至0.2）
    INTELLECTUAL_TO_INTELLECTUAL_FOLLOW_RATIO_MAX = 0.4  # 最大比例（从0.8降至0.4）

    # 群体内部关注关系配置（正态分布参数）
    # 普通群众内部关注关系的正态分布参数 - 降低连接密度实验
    REGULAR_FOLLOW_MEAN_RATIO = 0.25  # 均值比例（从0.5降至0.25）
    REGULAR_FOLLOW_STD_RATIO = 0.01   # 标准差比例（从0.02降至0.01）
    REGULAR_FOLLOW_MAX_RATIO = 0.4   # 最大粉丝数比例（从0.8降至0.4）

    # 用户选择偏向性配置
    INITIAL_BIAS_STRENGTH = 0  # 初始偏向性强度（0.0-1.0，0.0表示无偏向性，1.0表示最大偏向性）

    # 社区结构配置
    COMMUNITY_STRUCTURE_TYPE = "type_1"  # 社区结构类型：type_1（知识分子-群众）或 type_2（子社区结构）
    USE_EXPERIMENT_STRUCTURE = False     # 是否使用实验配置的社区结构
    
    @classmethod
    def get_multithreading_config(cls) -> Dict[str, Any]:
        """
        获取多线程配置

        Returns:
            包含多线程配置的字典
        """
        return {
            "enabled": cls._get_bool_env("ENABLE_MULTITHREADING", "true"),
            "max_workers": cls._get_int_env("MAX_WORKER_THREADS", "4"),
            "timeout": cls._get_int_env("THREAD_TIMEOUT", "30")
        }
    
    @classmethod
    def get_ai_service_config(cls) -> Dict[str, str]:
        """
        获取AI服务配置
        
        Returns:
            包含AI服务配置的字典
        """
        return {
            "api_key": cls.AI_SERVICE_API_KEY,
            "base_url": cls.AI_SERVICE_BASE_URL,
            "embedding_url": cls.AI_SERVICE_EMBEDDING_URL,
            "reranker_url": cls.AI_SERVICE_RERANKER_URL
        }
    
    @classmethod
    def get_database_config(cls) -> Dict[str, Any]:
        """
        获取数据库配置
        
        Returns:
            包含数据库配置的字典
        """
        return {
            "url": cls.ELASTICSEARCH_URL,
            "timeout": cls.ELASTICSEARCH_TIMEOUT,
            "max_retries": cls.ELASTICSEARCH_MAX_RETRIES
        }
    
    @classmethod
    def get_belief_config(cls) -> Dict[str, float]:
        """
        获取信念模块配置

        Returns:
            包含信念模块配置的字典
        """
        return {
            "embedding_threshold": cls.BELIEF_EMBEDDING_THRESHOLD,
            "rerank_threshold": cls.BELIEF_RERANK_THRESHOLD,
            "conflict_threshold": cls.BELIEF_CONFLICT_THRESHOLD
        }

    @classmethod
    def get_simulation_config(cls) -> Dict[str, Any]:
        """
        获取模拟运行配置

        Returns:
            包含模拟配置的字典
        """
        return {
            "steps": cls.SIMULATION_STEPS,
            "rounds": cls.SIMULATION_ROUNDS,
            "num_users": cls.NUM_USERS,
            "intellectual_ratio": cls.INTELLECTUAL_RATIO,
            "intellectual_belief_min": cls.INTELLECTUAL_BELIEF_MIN,
            "intellectual_belief_max": cls.INTELLECTUAL_BELIEF_MAX,
            "regular_belief_min": cls.REGULAR_BELIEF_MIN,
            "regular_belief_max": cls.REGULAR_BELIEF_MAX,
            "add_intellectuals": cls.ADD_INTELLECTUALS,
            "add_round": cls.ADD_ROUND,
            "num_intellectuals": cls.NUM_INTELLECTUALS,
            # 群体间关注关系配置
            "regular_to_intellectual_follow_ratio_min": cls.REGULAR_TO_INTELLECTUAL_FOLLOW_RATIO_MIN,
            "regular_to_intellectual_follow_ratio_max": cls.REGULAR_TO_INTELLECTUAL_FOLLOW_RATIO_MAX,
            "intellectual_to_regular_follow_ratio_min": cls.INTELLECTUAL_TO_REGULAR_FOLLOW_RATIO_MIN,
            "intellectual_to_regular_follow_ratio_max": cls.INTELLECTUAL_TO_REGULAR_FOLLOW_RATIO_MAX,
            "intellectual_to_intellectual_follow_ratio_min": cls.INTELLECTUAL_TO_INTELLECTUAL_FOLLOW_RATIO_MIN,
            "intellectual_to_intellectual_follow_ratio_max": cls.INTELLECTUAL_TO_INTELLECTUAL_FOLLOW_RATIO_MAX,
            # 群体内部关注关系配置
            "regular_follow_mean_ratio": cls.REGULAR_FOLLOW_MEAN_RATIO,
            "regular_follow_std_ratio": cls.REGULAR_FOLLOW_STD_RATIO,
            "regular_follow_max_ratio": cls.REGULAR_FOLLOW_MAX_RATIO,
            # 用户选择偏向性配置
            "initial_bias_strength": cls.INITIAL_BIAS_STRENGTH,
            # 社区结构配置
            "community_structure_type": cls.COMMUNITY_STRUCTURE_TYPE,
            "use_experiment_structure": cls.USE_EXPERIMENT_STRUCTURE
        }
