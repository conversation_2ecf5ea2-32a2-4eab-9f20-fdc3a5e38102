# 社区结构实验框架

本模块提供了用于对比不同社区结构的实验框架，支持两种主要的社区结构类型。

## 社区结构类型

### 1. 一类社区（Type 1）：知识分子-群众结构
- **描述**：社区中有少量比例的知识分子，有较多的粉丝数，其他普通群众粉丝数在较少的范围内正态分布
- **特点**：
  - 知识分子比例较低（默认10%）
  - 知识分子拥有更多粉丝
  - 普通群众粉丝数呈正态分布
  - 知识分子间有较高的相互关注率

### 2. 二类社区（Type 2）：子社区结构
- **描述**：社区分为若干的子社区，子社区内有较多人关注少部分的版主，且社区内人有相似的特质，不同的子社区之间联系较少
- **特点**：
  - 多个独立的子社区
  - 每个子社区有版主系统
  - 子社区内成员特质相似
  - 子社区间连接稀疏

## 使用方法

### 1. 配置社区结构类型

在 `config/config.py` 中设置：

```python
# 社区结构配置
COMMUNITY_STRUCTURE_TYPE = "type_1"  # 或 "type_2"
USE_EXPERIMENT_STRUCTURE = False     # 是否使用实验配置
```

### 2. 运行初始化

```bash
python scripts/initialize_db.py
```

### 3. 使用自定义实验配置

1. 创建实验配置文件（参考 `example_type1_config.json` 和 `example_type2_config.json`）
2. 设置 `USE_EXPERIMENT_STRUCTURE = True`
3. 在代码中加载特定的实验配置

### 4. 编程接口使用

```python
from experiment.structure import CommunityStructureType, CommunityStructureGenerator
from experiment.structure.relationship_builder import RelationshipBuilder

# 创建关系建立器
relationship_builder = RelationshipBuilder(agent_module)

# 建立一类社区结构
structure_info = relationship_builder.build_user_relationships(
    users, CommunityStructureType.TYPE_1
)

# 建立二类社区结构
structure_info = relationship_builder.build_user_relationships(
    users, CommunityStructureType.TYPE_2
)
```

## 配置参数说明

### 一类社区配置参数

- `intellectual_ratio`: 知识分子比例
- `regular_to_intellectual_follow_ratio_min/max`: 普通群众关注知识分子的比例范围
- `intellectual_to_regular_follow_ratio_min/max`: 知识分子关注普通群众的比例范围
- `intellectual_to_intellectual_follow_ratio_min/max`: 知识分子间关注比例范围
- `regular_follow_mean_ratio`: 普通群众粉丝数正态分布均值比例
- `regular_follow_std_ratio`: 普通群众粉丝数正态分布标准差比例
- `regular_follow_max_ratio`: 普通群众最大粉丝数比例

### 二类社区配置参数

- `num_subcommunities`: 子社区数量
- `subcommunity_size_min/max`: 子社区规模范围
- `moderators_per_subcommunity`: 每个子社区的版主数量
- `moderator_follower_ratio_min/max`: 版主在子社区内的粉丝比例范围
- `intra_community_follow_ratio_min/max`: 子社区内成员间关注比例范围
- `inter_community_follow_ratio_min/max`: 子社区间关注比例范围
- `moderator_to_moderator_follow_ratio_min/max`: 不同子社区版主间关注比例范围
- `personality_similarity_threshold`: 子社区内人格特质相似性阈值
- `cognitive_similarity_threshold`: 子社区内认知特质相似性阈值

## 实验对比

通过调整不同的配置参数，可以研究：

1. **信息传播速度**：不同结构下信息传播的快慢
2. **信息传播范围**：信息能够到达的用户比例
3. **观点极化程度**：不同结构对观点极化的影响
4. **社区稳定性**：结构对社区稳定性的影响

## 输出文件

运行后会在 `experiment/structure/` 目录下生成社区结构信息文件：
- `community_structure_type_1_YYYYMMDD_HHMMSS.json`
- `community_structure_type_2_YYYYMMDD_HHMMSS.json`

这些文件包含了详细的社区结构统计信息，可用于后续分析。
